"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useMenu } from '@/contexts/MenuContext'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import { UserMenu } from '@/components/auth/UserMenu'
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown'
import { Badge } from '@/components/ui/badge'
import {
  GraduationCap,
  Home,
  ShoppingBag,
  Palette,
  Info,
  Phone,
  Search,
  Heart,
  ExternalLink,
  FileText,
  Link as LinkIcon,
  ChevronDown,
  Grid3X3
} from 'lucide-react'

// أنواع البيانات لعناصر القائمة
interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
}

// نوع عنصر القائمة المعروض
interface NavItem {
  href: string
  label: string
  icon?: React.ReactElement
  target_type: 'internal' | 'external' | 'page'
  subItems?: {
    href: string
    label: string
    target_type: 'internal' | 'external' | 'page'
  }[]
}

export function Navigation() {
  const { t, locale } = useTranslation()
  const { cartCount, wishlistCount } = useCart()
  const { menuItems, loading } = useMenu()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])





  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون
  const getNavItemsFromDB = () => {
    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة
    const mainItems = menuItems.filter(item => !item.parent_id && item.is_active)

    return mainItems.map(item => {
      // اختيار العنوان حسب اللغة الحالية
      let label = item.title_ar // افتراضي
      if (locale === 'en' && item.title_en) {
        label = item.title_en
      } else if (locale === 'fr' && item.title_fr) {
        label = item.title_fr
      }

      // تحديد الرابط حسب نوع الهدف
      let href = item.target_value
      if (item.target_type === 'page') {
        href = `/pages/${item.target_value}`
      }

      // تحديد الأيقونة
      let icon = <LinkIcon className="h-4 w-4" />
      if (item.icon) {
        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون
        switch (item.icon) {
          case 'Home':
            icon = <Home className="h-4 w-4" />
            break
          case 'ShoppingBag':
            icon = <ShoppingBag className="h-4 w-4" />
            break
          case 'Palette':
            icon = <Palette className="h-4 w-4" />
            break
          case 'Search':
            icon = <Search className="h-4 w-4" />
            break
          case 'Info':
            icon = <Info className="h-4 w-4" />
            break
          case 'Phone':
            icon = <Phone className="h-4 w-4" />
            break
          case 'Grid3X3':
            icon = <Grid3X3 className="h-4 w-4" />
            break
          case 'ExternalLink':
            icon = <ExternalLink className="h-4 w-4" />
            break
          case 'FileText':
            icon = <FileText className="h-4 w-4" />
            break
          default:
            icon = <LinkIcon className="h-4 w-4" />
        }
      }

      // البحث عن القوائم الفرعية لهذا العنصر
      const subItems = menuItems
        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)
        .map(subItem => {
          let subLabel = subItem.title_ar
          if (locale === 'en' && subItem.title_en) {
            subLabel = subItem.title_en
          } else if (locale === 'fr' && subItem.title_fr) {
            subLabel = subItem.title_fr
          }

          let subHref = subItem.target_value
          if (subItem.target_type === 'page') {
            subHref = `/pages/${subItem.target_value}`
          }

          return {
            href: subHref,
            label: subLabel,
            target_type: subItem.target_type
          }
        })

      return {
        href,
        label,
        icon,
        target_type: item.target_type,
        subItems: subItems.length > 0 ? subItems : undefined
      }
    })
  }

  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)
  const defaultNavItems: NavItem[] = [
    {
      href: '/',
      label: t('navigation.home'),
      icon: <Home className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/catalog',
      label: t('navigation.catalog'),
      icon: <ShoppingBag className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/about',
      label: t('navigation.about') || 'من نحن',
      icon: <Info className="h-4 w-4" />,
      target_type: 'internal' as const
    },
    {
      href: '/contact',
      label: t('navigation.contact') || 'تواصل معنا',
      icon: <Phone className="h-4 w-4" />,
      target_type: 'internal' as const
    }
  ]

  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية
  const { user, profile } = useAuth()

  // تحديد عناصر القائمة بناءً على الحالة
  let allNavItems: NavItem[]

  if (loading) {
    // أثناء التحميل، استخدم القائمة الافتراضية
    allNavItems = defaultNavItems
  } else if (menuItems.length > 0) {
    // إذا كانت هناك عناصر من قاعدة البيانات، استخدمها فقط
    allNavItems = getNavItemsFromDB()
  } else {
    // إذا لم تكن هناك عناصر من قاعدة البيانات، استخدم القائمة الافتراضية
    allNavItems = defaultNavItems
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-3">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center gap-3 hover:opacity-80 transition-all duration-300 group"
          >
            <div className="relative">
              <GraduationCap className="h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-gray-900 dark:text-white leading-tight">
                Graduation Toqs
              </span>
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-1">
            {allNavItems.map((item) => {
              // تحديد ما إذا كان الرابط خارجي
              const isExternal = item.target_type === 'external'
              const hasSubItems = item.subItems && item.subItems.length > 0

              // إذا كان العنصر له قوائم فرعية
              if (hasSubItems) {
                return (
                  <div key={item.href} className="relative group">
                    <button
                      className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${
                        isActive(item.href)
                          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                      }`}
                    >
                      <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>
                        {item.icon}
                      </span>
                      <span className="text-sm font-medium">
                        {item.label}
                      </span>
                      <ChevronDown className="h-3 w-3 transition-transform group-hover:rotate-180" />
                    </button>

                    {/* القائمة الفرعية */}
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                      <div className="py-2">
                        {item.subItems?.map((subItem) => {
                          const subIsExternal = subItem.target_type === 'external'
                          const subLinkProps = subIsExternal
                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }
                            : { href: subItem.href }

                          return (
                            <Link
                              key={subItem.href}
                              {...subLinkProps}
                              className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                            >
                              {subItem.label}
                              {subIsExternal && (
                                <ExternalLink className="h-3 w-3 opacity-60" />
                              )}
                            </Link>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                )
              }

              // العناصر العادية بدون قوائم فرعية
              const linkProps = isExternal
                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }
                : { href: item.href }

              return (
                <Link
                  key={item.href}
                  {...linkProps}
                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${
                    isActive(item.href)
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                  }`}
                >
                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>
                    {item.icon}
                  </span>
                  <span className="text-sm font-medium">
                    {item.label}
                  </span>
                  {isExternal && (
                    <ExternalLink className="h-3 w-3 opacity-60" />
                  )}
                  {isActive(item.href) && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"></div>
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center gap-2">
            {/* Wishlist */}
            <Button variant="ghost" size="sm" className="relative group" asChild>
              <Link href="/wishlist">
                <Heart className="h-5 w-5 transition-colors group-hover:text-red-500" />
                {wishlistCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse"
                  >
                    {wishlistCount > 99 ? '99+' : wishlistCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Cart Icon */}
            <Button variant="ghost" size="sm" className="relative group" asChild>
              <Link href="/cart">
                <ShoppingBag className="h-5 w-5 transition-colors group-hover:text-blue-600" />
                {cartCount > 0 && (
                  <Badge
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse"
                  >
                    {cartCount > 99 ? '99+' : cartCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Notifications */}
            <NotificationDropdown />

            {/* Divider */}
            <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

            {/* Language & Theme Controls */}
            <div className="flex items-center gap-1">
              <LanguageToggle />
              <ThemeToggle />
            </div>

            {/* User Menu */}
            <UserMenu />
          </div>

          {/* Mobile Actions */}
          <div className="flex lg:hidden items-center gap-2">
            {/* Mobile Cart */}
            <Button variant="ghost" size="sm" className="relative" asChild>
              <Link href="/cart">
                <ShoppingBag className="h-5 w-5" />
                {cartCount > 0 && (
                  <Badge
                    className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600"
                  >
                    {cartCount > 9 ? '9+' : cartCount}
                  </Badge>
                )}
              </Link>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="relative"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div className="relative w-6 h-6 flex items-center justify-center">
                <span
                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'
                  }`}
                />
                <span
                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${
                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'
                  }`}
                />
                <span
                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'
                  }`}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${
            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="border-t border-gray-200 dark:border-gray-700 py-4">
            <nav className="flex flex-col gap-1 mb-6">
              {allNavItems.map((item, index) => {
                // تحديد ما إذا كان الرابط خارجي
                const isExternal = item.target_type === 'external'
                const hasSubItems = item.subItems && item.subItems.length > 0

                // إذا كان العنصر له قوائم فرعية
                if (hasSubItems) {
                  return (
                    <div key={item.href} className="mx-2">
                      <div
                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${
                          isActive(item.href)
                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                        }`}
                        style={{
                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',
                          animationDuration: '0.3s',
                          animationTimingFunction: 'ease-out',
                          animationFillMode: 'forwards',
                          animationDelay: `${index * 50}ms`
                        }}
                      >
                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>
                          {item.icon}
                        </span>
                        <span className="text-sm flex-1">
                          {item.label}
                        </span>
                        <ChevronDown className="h-4 w-4" />
                      </div>

                      {/* القوائم الفرعية للموبايل */}
                      <div className="ml-6 mt-2 space-y-1">
                        {item.subItems?.map((subItem) => {
                          const subIsExternal = subItem.target_type === 'external'
                          const subLinkProps = subIsExternal
                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }
                            : { href: subItem.href }

                          return (
                            <Link
                              key={subItem.href}
                              {...subLinkProps}
                              onClick={() => setIsMobileMenuOpen(false)}
                              className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                            >
                              {subItem.label}
                              {subIsExternal && (
                                <ExternalLink className="h-3 w-3 opacity-60" />
                              )}
                            </Link>
                          )
                        })}
                      </div>
                    </div>
                  )
                }

                // العناصر العادية بدون قوائم فرعية
                const linkProps = isExternal
                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }
                  : { href: item.href }

                return (
                  <Link
                    key={item.href}
                    {...linkProps}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${
                      isActive(item.href)
                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                    }`}
                    style={{
                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',
                      animationDuration: '0.3s',
                      animationTimingFunction: 'ease-out',
                      animationFillMode: 'forwards',
                      animationDelay: `${index * 50}ms`
                    }}
                  >
                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>
                      {item.icon}
                    </span>
                    <span className="text-sm">
                      {item.label}
                    </span>
                    {isExternal && (
                      <ExternalLink className="h-3 w-3 opacity-60" />
                    )}
                  </Link>
                )
              })}
            </nav>

            {/* Mobile Actions */}
            <div className="flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <LanguageToggle />
                <ThemeToggle />
              </div>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" className="relative" asChild>
                  <Link href="/wishlist">
                    <Heart className="h-5 w-5" />
                    {wishlistCount > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs"
                      >
                        {wishlistCount > 9 ? '9+' : wishlistCount}
                      </Badge>
                    )}
                  </Link>
                </Button>
                <UserMenu />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Navigation
